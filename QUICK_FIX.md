# 快速解决 "Key limit exceeded" 错误

## 问题描述
您遇到的错误：
```
OpenRouter API error: {"error":{"message":"Key limit exceeded. Manage it using https://openrouter.ai/settings/keys","code":403}}
```

## 立即解决方案

### 方案 1: 使用测试模式 (推荐，立即可用)

```bash
# 使用更便宜的 Claude-3 Haiku 模型
cargo run -- --test-mode --interactive
```

这将自动使用 `anthropic/claude-3-haiku-********` 模型，成本更低。

### 方案 2: 手动指定便宜模型

```bash
# 直接指定 Haiku 模型
cargo run -- --model "anthropic/claude-3-haiku-********" --interactive
```

### 方案 3: 测试 API 连接

```bash
# 运行 API 测试工具
cargo run --example test_api
```

## 长期解决方案

### 1. 管理 OpenRouter 密钥限制

1. 访问 [OpenRouter 密钥设置](https://openrouter.ai/settings/keys)
2. 找到您的 API 密钥
3. 增加使用限制或创建新密钥

### 2. 账户充值

1. 访问 [OpenRouter 账户页面](https://openrouter.ai/account)
2. 添加信用额度
3. 设置合理的使用限制

## 模型成本对比

| 模型 | 输入成本 (每1M tokens) | 输出成本 (每1M tokens) | 推荐用途 |
|------|----------------------|----------------------|----------|
| Claude-3 Haiku | $0.25 | $1.25 | 测试、简单任务 |
| Claude-3 Sonnet | $3.00 | $15.00 | 日常使用 |
| Claude-3 Opus | $15.00 | $75.00 | 复杂任务 |

## 使用建议

### 开发和测试阶段
```bash
# 使用测试模式
cargo run -- --test-mode --interactive
```

### 生产使用
```bash
# 使用 Sonnet (平衡性能和成本)
cargo run -- --model "anthropic/claude-3-sonnet-********" --interactive
```

### 复杂任务
```bash
# 使用 Opus (最强性能)
cargo run -- --model "anthropic/claude-3-opus-********" --interactive
```

## 错误预防

1. **设置使用限制**: 在 OpenRouter 控制台设置每日/每月限制
2. **监控使用**: 定期检查使用情况
3. **选择合适模型**: 根据任务复杂度选择模型
4. **使用测试模式**: 开发时使用 `--test-mode`

## 立即可用的命令

如果您想立即开始使用，运行以下命令：

```bash
# 确保设置了 API 密钥
export OPENROUTER_API_KEY="your-api-key-here"

# 使用测试模式启动
cargo run -- --test-mode --interactive
```

这将使用最便宜的模型，让您可以立即开始测试系统功能。
