[package]
name = "minimal_agent"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A minimal agent system based on LLM-driven tool calling"
license = "MIT"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"
futures = "0.3"

# HTTP client for LLM API calls
reqwest = { version = "0.11", features = ["json"] }

# JSON serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# UUID generation
uuid = { version = "1.0", features = ["v4", "serde"] }

# Date and time
chrono = { version = "0.4", features = ["serde"] }

# Configuration
config = "0.14"

# CLI
clap = { version = "4.0", features = ["derive"] }

[dev-dependencies]
tokio-test = "0.4"
