use minimal_agent::{
    Agent, AgentConfig, LlmConfig,
    tools::ToolRegistry,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=info")
        .init();

    println!("🔍 API 连接测试工具");
    println!("==================");

    // 检查环境变量
    let api_key = std::env::var("OPENROUTER_API_KEY")
        .or_else(|_| std::env::var("OPENAI_API_KEY"))
        .unwrap_or_else(|_| {
            eprintln!("❌ 错误: 未找到 API 密钥");
            eprintln!("请设置 OPENROUTER_API_KEY 或 OPENAI_API_KEY 环境变量");
            std::process::exit(1);
        });

    println!("✅ 找到 API 密钥: {}...{}", 
             &api_key[..8.min(api_key.len())], 
             &api_key[api_key.len().saturating_sub(4)..]);

    // 使用最便宜的模型进行测试
    let config = AgentConfig {
        llm: LlmConfig {
            provider: "openrouter".to_string(),
            model: "anthropic/claude-3-haiku-20240307".to_string(), // 最便宜的模型
            api_key,
            base_url: None,
            temperature: Some(0.1),
            max_tokens: Some(100), // 限制 token 数量
        },
        max_iterations: 1,
        max_tool_calls_per_iteration: 1,
        system_prompt: Some("You are a test assistant. Respond briefly.".to_string()),
    };

    // 创建简单的 Agent（不注册工具以减少复杂性）
    let tool_registry = ToolRegistry::new();
    let mut agent = Agent::new(config, tool_registry);

    println!("🚀 开始 API 连接测试...");
    
    // 开始对话
    agent.start_conversation();

    // 发送简单的测试消息
    let test_message = "Hello! Please respond with just 'API test successful'.";
    
    match agent.process_user_input(test_message.to_string()).await {
        Ok(response) => {
            println!("✅ API 连接成功!");
            println!("📝 响应: {}", response);
            println!("\n🎉 您的 API 密钥工作正常，可以开始使用 Minimal Agent 了！");
        }
        Err(e) => {
            println!("❌ API 连接失败: {}", e);
            println!("\n🔧 故障排除建议:");
            
            let error_str = e.to_string();
            if error_str.contains("401") {
                println!("   • 检查 API 密钥是否正确");
                println!("   • 确保密钥有效且未过期");
            } else if error_str.contains("402") {
                println!("   • 检查账户余额");
                println!("   • 在 OpenRouter 控制台充值");
            } else if error_str.contains("403") {
                println!("   • API 密钥限制已达上限");
                println!("   • 访问 https://openrouter.ai/settings/keys 管理限制");
            } else if error_str.contains("429") {
                println!("   • 请求过于频繁，请稍后重试");
            } else {
                println!("   • 检查网络连接");
                println!("   • 查看详细错误信息: {}", e);
            }
            
            std::process::exit(1);
        }
    }

    Ok(())
}
