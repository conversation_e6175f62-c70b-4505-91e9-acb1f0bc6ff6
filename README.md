# Minimal Agent

一个基于 Rust 构建的最小化 Agent 系统，采用 Agentic 模式，支持大语言模型驱动的工具调用。

## 特性

- 🦀 **Rust 实现** - 高性能、内存安全的 Agent 系统
- 🧠 **LLM 驱动** - 支持 OpenRouter Claude-3 Opus、OpenAI GPT 等模型的智能决策
- 🔧 **工具系统** - 可扩展的工具注册和执行框架
- 💬 **对话管理** - 完整的对话状态管理和历史记录
- 🔄 **推理循环** - 自动化的工具调用和结果处理
- 📊 **状态管理** - 清晰的 Agent 状态跟踪
- 🌐 **多提供商支持** - OpenRouter、OpenAI 等多个 LLM 提供商

## 架构设计

### 核心组件

1. **Agent 控制器** (`src/agent/controller.rs`)
   - 主要的推理循环逻辑
   - 用户输入处理
   - LLM 响应处理

2. **LLM 接口** (`src/llm/`)
   - 与大语言模型 API 通信
   - 支持 OpenRouter、OpenAI 格式的请求/响应

3. **工具系统** (`src/tools/`)
   - 工具注册表和执行器
   - 内置工具（Echo、Calculator）
   - 可扩展的工具接口

4. **状态管理** (`src/agent/state.rs`)
   - 对话状态跟踪
   - 消息历史管理

## 快速开始

### 安装依赖

确保您已安装 Rust 1.70+：

```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

### 设置 API 密钥

对于 OpenRouter (推荐，支持 Claude-4 Opus):
```bash
export OPENROUTER_API_KEY="your-openrouter-api-key-here"
```

对于 OpenAI:
```bash
export OPENAI_API_KEY="your-openai-api-key-here"
```

> 📖 **详细的 OpenRouter 配置指南**: 请参阅 [OPENROUTER_GUIDE.md](./OPENROUTER_GUIDE.md)

### 构建项目

```bash
cargo build --release
```

### 运行 Agent

#### 交互模式

```bash
cargo run -- --interactive
```

#### 单次查询模式

```bash
cargo run
```

#### 指定模型和提供商

使用 Claude-4 Opus (默认):
```bash
cargo run -- --interactive
```

使用 OpenAI GPT-4:
```bash
cargo run -- --provider openai --model gpt-4-turbo-preview --interactive
```

使用其他 Claude 模型:
```bash
cargo run -- --model anthropic/claude-3-sonnet-20240229 --interactive
```

## 使用示例

### 基本对话

```
👤 You: Hello, can you help me with some calculations?
🤖 Agent: Hello! I'd be happy to help you with calculations. I have access to a calculator tool that can perform basic arithmetic operations like addition, subtraction, multiplication, and division. What would you like me to calculate?
```

### 工具调用示例

```
👤 You: What is 15 * 25?
🤖 Agent: I'll calculate 15 * 25 for you using the calculator tool.

[Tool Call: calculator with operation=multiply, a=15, b=25]

The result of 15 * 25 is 375.
```

### 可用命令

在交互模式下，您可以使用以下命令：

- `help` - 显示帮助信息
- `status` - 显示 Agent 状态
- `history` - 显示对话历史
- `quit` / `exit` - 退出程序

## 内置工具

### Echo 工具
- **功能**: 回显输入消息
- **用途**: 测试工具执行系统
- **示例**: "echo hello world"

### Calculator 工具
- **功能**: 基本算术运算
- **支持操作**: 加法、减法、乘法、除法
- **示例**: "calculate 10 + 5"

## 扩展开发

### 添加新工具

1. 实现 `ToolTrait` 特征：

```rust
use crate::tools::ToolTrait;
use async_trait::async_trait;

pub struct MyCustomTool;

#[async_trait]
impl ToolTrait for MyCustomTool {
    fn name(&self) -> &str {
        "my_tool"
    }

    fn description(&self) -> &str {
        "Description of what my tool does"
    }

    fn parameters_schema(&self) -> serde_json::Value {
        // JSON Schema for tool parameters
    }

    async fn execute(&self, parameters: serde_json::Value) -> Result<ToolExecutionResult> {
        // Tool implementation
    }
}
```

2. 在 `main.rs` 中注册工具：

```rust
tool_registry.register_tool(Arc::new(MyCustomTool))?;
```

### 配置选项

Agent 支持以下配置选项：

- `max_iterations`: 最大推理循环次数
- `max_tool_calls_per_iteration`: 每次迭代最大工具调用数
- `system_prompt`: 系统提示词
- LLM 配置（模型、温度、最大 token 数等）

## 项目结构

```
src/
├── agent/              # Agent 核心逻辑
│   ├── controller.rs   # 主控制器
│   ├── state.rs        # 状态管理
│   └── mod.rs
├── llm/                # LLM 接口
│   ├── client.rs       # LLM 客户端
│   ├── types.rs        # LLM 类型定义
│   └── mod.rs
├── tools/              # 工具系统
│   ├── builtin/        # 内置工具
│   ├── executor.rs     # 工具执行器
│   ├── registry.rs     # 工具注册表
│   └── mod.rs
├── types.rs            # 全局类型定义
├── error.rs            # 错误处理
├── lib.rs              # 库入口
└── main.rs             # 程序入口
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
