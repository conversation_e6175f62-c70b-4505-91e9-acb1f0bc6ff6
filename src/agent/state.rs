use crate::types::{AgentState, Conversation, Message};
use std::collections::HashMap;
use uuid::Uuid;

/// Agent 状态管理器
#[derive(Debug, Clone)]
pub struct AgentStateManager {
    conversations: HashMap<Uuid, Conversation>,
    current_conversation_id: Option<Uuid>,
}

impl AgentStateManager {
    /// 创建新的状态管理器
    pub fn new() -> Self {
        Self {
            conversations: HashMap::new(),
            current_conversation_id: None,
        }
    }

    /// 创建新的对话
    pub fn create_conversation(&mut self) -> Uuid {
        let conversation = Conversation::new();
        let id = conversation.id;
        self.conversations.insert(id, conversation);
        self.current_conversation_id = Some(id);
        id
    }

    /// 获取当前对话
    pub fn get_current_conversation(&self) -> Option<&Conversation> {
        self.current_conversation_id
            .and_then(|id| self.conversations.get(&id))
    }

    /// 获取当前对话（可变引用）
    pub fn get_current_conversation_mut(&mut self) -> Option<&mut Conversation> {
        self.current_conversation_id
            .and_then(|id| self.conversations.get_mut(&id))
    }

    /// 获取指定对话
    pub fn get_conversation(&self, id: &Uuid) -> Option<&Conversation> {
        self.conversations.get(id)
    }

    /// 获取指定对话（可变引用）
    pub fn get_conversation_mut(&mut self, id: &Uuid) -> Option<&mut Conversation> {
        self.conversations.get_mut(id)
    }

    /// 设置当前对话
    pub fn set_current_conversation(&mut self, id: Uuid) -> bool {
        if self.conversations.contains_key(&id) {
            self.current_conversation_id = Some(id);
            true
        } else {
            false
        }
    }

    /// 添加消息到当前对话
    pub fn add_message_to_current(&mut self, message: Message) -> Result<(), String> {
        match self.get_current_conversation_mut() {
            Some(conversation) => {
                conversation.add_message(message);
                Ok(())
            }
            None => Err("No current conversation".to_string()),
        }
    }

    /// 设置当前对话状态
    pub fn set_current_state(&mut self, state: AgentState) -> Result<(), String> {
        match self.get_current_conversation_mut() {
            Some(conversation) => {
                conversation.set_state(state);
                Ok(())
            }
            None => Err("No current conversation".to_string()),
        }
    }

    /// 获取当前对话状态
    pub fn get_current_state(&self) -> Option<&AgentState> {
        self.get_current_conversation().map(|conv| &conv.state)
    }

    /// 获取所有对话 ID
    pub fn get_conversation_ids(&self) -> Vec<Uuid> {
        self.conversations.keys().cloned().collect()
    }

    /// 删除对话
    pub fn remove_conversation(&mut self, id: &Uuid) -> bool {
        if self.current_conversation_id == Some(*id) {
            self.current_conversation_id = None;
        }
        self.conversations.remove(id).is_some()
    }

    /// 清除所有对话
    pub fn clear_conversations(&mut self) {
        self.conversations.clear();
        self.current_conversation_id = None;
    }

    /// 获取对话数量
    pub fn conversation_count(&self) -> usize {
        self.conversations.len()
    }
}

impl Default for AgentStateManager {
    fn default() -> Self {
        Self::new()
    }
}
