use crate::agent::state::AgentStateManager;
use crate::llm::{LlmClient, ToolChoice};
use crate::tools::{ToolExecutor, ToolRegistry};
use crate::types::{
    AgentConfig, AgentState, Message, MessageRole, Tool<PERSON>all, ToolCallResult,
};
use crate::{AgentError, Result};
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Agent 主控制器，负责整个 Agent 系统的核心逻辑
pub struct Agent {
    config: AgentConfig,
    llm_client: LlmClient,
    tool_registry: Arc<ToolRegistry>,
    tool_executor: ToolExecutor,
    state_manager: AgentStateManager,
}

impl Agent {
    /// 创建新的 Agent 实例
    pub fn new(config: AgentConfig, tool_registry: ToolRegistry) -> Self {
        let llm_client = LlmClient::new(config.llm.clone());
        let tool_registry = Arc::new(tool_registry);
        let tool_executor = ToolExecutor::new(Arc::clone(&tool_registry));
        let state_manager = AgentStateManager::new();

        Self {
            config,
            llm_client,
            tool_registry,
            tool_executor,
            state_manager,
        }
    }

    /// 开始新的对话
    pub fn start_conversation(&mut self) -> Uuid {
        let conversation_id = self.state_manager.create_conversation();
        
        // 添加系统消息
        if let Some(system_prompt) = &self.config.system_prompt {
            let system_message = Message::new_system(system_prompt.clone());
            if let Err(e) = self.state_manager.add_message_to_current(system_message) {
                error!("Failed to add system message: {}", e);
            }
        }

        info!("Started new conversation: {}", conversation_id);
        conversation_id
    }

    /// 处理用户输入
    pub async fn process_user_input(&mut self, input: String) -> Result<String> {
        info!("Processing user input: {}", input);

        // 确保有当前对话
        if self.state_manager.get_current_conversation().is_none() {
            self.start_conversation();
        }

        // 添加用户消息
        let user_message = Message::new_user(input);
        self.state_manager.add_message_to_current(user_message)
            .map_err(|e| AgentError::agent_state(e))?;

        // 设置状态为处理中
        self.state_manager.set_current_state(AgentState::Processing)
            .map_err(|e| AgentError::agent_state(e))?;

        // 执行主要的推理循环
        let response = self.reasoning_loop().await?;

        // 设置状态为完成
        self.state_manager.set_current_state(AgentState::Completed)
            .map_err(|e| AgentError::agent_state(e))?;

        info!("Completed processing user input");
        Ok(response)
    }

    /// 主要的推理循环
    async fn reasoning_loop(&mut self) -> Result<String> {
        let mut iteration = 0;
        let max_iterations = self.config.max_iterations;

        while iteration < max_iterations {
            iteration += 1;
            info!("Reasoning loop iteration: {}/{}", iteration, max_iterations);

            // 获取当前对话的所有消息
            let messages = self.get_conversation_messages()?;

            // 获取工具定义
            let tool_definitions = self.tool_registry.get_tool_definitions();

            // 调用 LLM
            let llm_response = self.llm_client
                .chat_completion(
                    messages,
                    if tool_definitions.is_empty() { None } else { Some(tool_definitions) },
                    Some(ToolChoice::Auto),
                )
                .await?;

            // 处理 LLM 响应
            let assistant_message = self.process_llm_response(llm_response).await?;

            // 如果没有工具调用，说明对话结束
            if assistant_message.tool_calls.is_none() {
                return Ok(assistant_message.content);
            }

            // 如果有工具调用但已达到最大迭代次数，返回错误
            if iteration >= max_iterations {
                warn!("Reached maximum iterations with pending tool calls");
                return Err(AgentError::agent_state(
                    "Reached maximum iterations with pending tool calls".to_string(),
                ));
            }
        }

        Err(AgentError::agent_state(
            "Reasoning loop completed without final response".to_string(),
        ))
    }

    /// 处理 LLM 响应
    async fn process_llm_response(
        &mut self,
        llm_response: crate::llm::LlmResponse,
    ) -> Result<Message> {
        let llm_message = llm_response.message;

        // 创建 Assistant 消息
        let mut assistant_message = Message::new_assistant(
            llm_message.content.unwrap_or_default(),
        );

        // 处理工具调用
        if let Some(llm_tool_calls) = llm_message.tool_calls {
            let tool_calls: Vec<ToolCall> = llm_tool_calls
                .into_iter()
                .map(|call| call.into())
                .collect();

            debug!("Processing {} tool calls", tool_calls.len());

            // 验证工具调用
            self.tool_executor.validate_tool_calls(&tool_calls)?;

            // 执行工具调用
            let tool_results = self.tool_executor.execute_tool_calls(&tool_calls).await;

            // 设置工具调用和结果
            assistant_message.tool_calls = Some(tool_calls);
            assistant_message.tool_call_results = Some(tool_results.clone());

            // 添加 Assistant 消息到对话
            self.state_manager.add_message_to_current(assistant_message.clone())
                .map_err(|e| AgentError::agent_state(e))?;

            // 为每个工具调用结果创建工具消息
            for result in tool_results {
                let tool_message = self.create_tool_message(result)?;
                self.state_manager.add_message_to_current(tool_message)
                    .map_err(|e| AgentError::agent_state(e))?;
            }
        } else {
            // 没有工具调用，直接添加消息
            self.state_manager.add_message_to_current(assistant_message.clone())
                .map_err(|e| AgentError::agent_state(e))?;
        }

        Ok(assistant_message)
    }

    /// 创建工具消息
    fn create_tool_message(&self, tool_result: ToolCallResult) -> Result<Message> {
        let content = match tool_result.result {
            crate::types::ToolExecutionResult::Success { output } => {
                format!("Tool execution successful: {}", output)
            }
            crate::types::ToolExecutionResult::Error { error } => {
                format!("Tool execution failed: {}", error)
            }
        };

        let message = Message {
            id: Uuid::new_v4(),
            role: MessageRole::Tool,
            content,
            timestamp: chrono::Utc::now(),
            tool_calls: None,
            tool_call_results: None,
        };

        Ok(message)
    }

    /// 获取当前对话的所有消息
    fn get_conversation_messages(&self) -> Result<Vec<Message>> {
        match self.state_manager.get_current_conversation() {
            Some(conversation) => Ok(conversation.messages.clone()),
            None => Err(AgentError::agent_state("No current conversation")),
        }
    }

    /// 获取工具注册表
    pub fn get_tool_registry(&self) -> &ToolRegistry {
        &self.tool_registry
    }

    /// 获取当前对话状态
    pub fn get_current_state(&self) -> Option<&AgentState> {
        self.state_manager.get_current_state()
    }

    /// 获取对话历史
    pub fn get_conversation_history(&self) -> Option<Vec<Message>> {
        self.state_manager
            .get_current_conversation()
            .map(|conv| conv.messages.clone())
    }
}
