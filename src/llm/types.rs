use serde::{Deserialize, Serialize};
use crate::types::{Message, ToolCall};
use crate::tools::ToolDefinition;

/// LLM 请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmRequest {
    pub messages: Vec<LlmMessage>,
    pub tools: Option<Vec<ToolDefinition>>,
    pub tool_choice: Option<ToolChoice>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    pub model: String,
}

/// LLM 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmResponse {
    pub message: LlmMessage,
    pub usage: Option<Usage>,
    pub finish_reason: Option<String>,
}

/// LLM 消息格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmMessage {
    pub role: String,
    pub content: Option<String>,
    pub tool_calls: Option<Vec<LlmToolCall>>,
    pub tool_call_id: Option<String>,
}

/// LLM 工具调用格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmToolCall {
    pub id: String,
    pub r#type: String,
    pub function: LlmFunction,
}

/// LLM 函数调用
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmFunction {
    pub name: String,
    pub arguments: String,
}

/// 工具选择策略
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ToolChoice {
    Auto,
    None,
    Required,
    Specific { function: SpecificFunction },
}

/// 特定函数选择
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpecificFunction {
    pub name: String,
}

/// 使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Usage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

impl From<&Message> for LlmMessage {
    fn from(message: &Message) -> Self {
        let role = match message.role {
            crate::types::MessageRole::User => "user".to_string(),
            crate::types::MessageRole::Assistant => "assistant".to_string(),
            crate::types::MessageRole::System => "system".to_string(),
            crate::types::MessageRole::Tool => "tool".to_string(),
        };

        let tool_calls = message.tool_calls.as_ref().map(|calls| {
            calls
                .iter()
                .map(|call| LlmToolCall {
                    id: call.id.clone(),
                    r#type: "function".to_string(),
                    function: LlmFunction {
                        name: call.name.clone(),
                        arguments: call.parameters.to_string(),
                    },
                })
                .collect()
        });

        Self {
            role,
            content: Some(message.content.clone()),
            tool_calls,
            tool_call_id: None,
        }
    }
}

impl From<LlmToolCall> for ToolCall {
    fn from(llm_call: LlmToolCall) -> Self {
        Self {
            id: llm_call.id,
            name: llm_call.function.name,
            parameters: serde_json::from_str(&llm_call.function.arguments)
                .unwrap_or(serde_json::Value::Object(serde_json::Map::new())),
        }
    }
}
