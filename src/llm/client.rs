use crate::llm::types::{LlmRequest, LlmResponse, ToolChoice};
use crate::types::{LlmConfig, Message};
use crate::tools::ToolDefinition;
use crate::{AgentError, Result};
use reqwest::Client;
use serde_json::json;
use tracing::{debug, error, info};

/// LLM 客户端，负责与大语言模型 API 通信
pub struct LlmClient {
    config: LlmConfig,
    client: Client,
}

impl LlmClient {
    /// 创建新的 LLM 客户端
    pub fn new(config: LlmConfig) -> Self {
        Self {
            config,
            client: Client::new(),
        }
    }

    /// 发送聊天完成请求
    pub async fn chat_completion(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<ToolDefinition>>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<LlmResponse> {
        info!("Sending chat completion request with {} messages", messages.len());
        
        let llm_messages: Vec<_> = messages.iter().map(|m| m.into()).collect();
        
        let request = LlmRequest {
            messages: llm_messages,
            tools,
            tool_choice,
            temperature: self.config.temperature,
            max_tokens: self.config.max_tokens,
            model: self.config.model.clone(),
        };

        debug!("LLM request: {:?}", request);

        let response = match self.config.provider.as_str() {
            "openai" => self.send_openai_request(&request).await?,
            "anthropic" => self.send_anthropic_request(&request).await?,
            _ => {
                return Err(AgentError::configuration(format!(
                    "Unsupported LLM provider: {}",
                    self.config.provider
                )));
            }
        };

        debug!("LLM response: {:?}", response);
        info!("Received chat completion response");

        Ok(response)
    }

    /// 发送 OpenAI API 请求
    async fn send_openai_request(&self, request: &LlmRequest) -> Result<LlmResponse> {
        let url = self
            .config
            .base_url
            .as_ref()
            .map(|base| format!("{}/chat/completions", base))
            .unwrap_or_else(|| "https://api.openai.com/v1/chat/completions".to_string());

        let mut payload = json!({
            "model": request.model,
            "messages": request.messages,
        });

        if let Some(temp) = request.temperature {
            payload["temperature"] = json!(temp);
        }

        if let Some(max_tokens) = request.max_tokens {
            payload["max_tokens"] = json!(max_tokens);
        }

        if let Some(tools) = &request.tools {
            payload["tools"] = json!(tools.iter().map(|tool| {
                json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());

            if let Some(tool_choice) = &request.tool_choice {
                payload["tool_choice"] = match tool_choice {
                    ToolChoice::Auto => json!("auto"),
                    ToolChoice::None => json!("none"),
                    ToolChoice::Required => json!("required"),
                    ToolChoice::Specific { function } => json!({
                        "type": "function",
                        "function": {
                            "name": function.name
                        }
                    }),
                };
            }
        }

        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("OpenAI API error: {}", error_text);
            return Err(AgentError::other(format!("OpenAI API error: {}", error_text)));
        }

        let response_json: serde_json::Value = response.json().await?;
        
        // 解析 OpenAI 响应格式
        let choice = response_json["choices"][0].clone();
        let message = choice["message"].clone();
        
        let llm_response = LlmResponse {
            message: serde_json::from_value(message)?,
            usage: response_json.get("usage").map(|u| serde_json::from_value(u.clone()).ok()).flatten(),
            finish_reason: choice.get("finish_reason").and_then(|v| v.as_str()).map(|s| s.to_string()),
        };

        Ok(llm_response)
    }

    /// 发送 Anthropic API 请求（简化实现）
    async fn send_anthropic_request(&self, _request: &LlmRequest) -> Result<LlmResponse> {
        // 这里可以实现 Anthropic Claude API 的调用
        // 目前返回一个错误，表示尚未实现
        Err(AgentError::configuration(
            "Anthropic provider not yet implemented".to_string(),
        ))
    }
}
