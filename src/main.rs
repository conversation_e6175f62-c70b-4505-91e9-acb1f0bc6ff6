use clap::{Arg, Command};
use minimal_agent::{
    Agent, AgentConfig, LlmConfig,
    tools::{ToolRegistry, builtin::{EchoTool, CalculatorTool}},
};
use std::io::{self, Write};
use std::sync::Arc;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=info")
        .init();

    // 解析命令行参数
    let matches = Command::new("Minimal Agent")
        .version("0.1.0")
        .about("A minimal agent system based on LLM-driven tool calling")
        .arg(
            Arg::new("api-key")
                .long("api-key")
                .value_name("KEY")
                .help("OpenAI API key")
                .required(false),
        )
        .arg(
            Arg::new("model")
                .long("model")
                .value_name("MODEL")
                .help("LLM model to use")
                .default_value("gpt-3.5-turbo"),
        )
        .arg(
            Arg::new("interactive")
                .short('i')
                .long("interactive")
                .help("Run in interactive mode")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    // 获取 API 密钥
    let api_key = matches
        .get_one::<String>("api-key")
        .cloned()
        .or_else(|| std::env::var("OPENAI_API_KEY").ok())
        .unwrap_or_else(|| {
            eprintln!("Warning: No API key provided. Set OPENAI_API_KEY environment variable or use --api-key");
            "your-api-key-here".to_string()
        });

    let model = matches.get_one::<String>("model").unwrap().clone();

    // 创建 Agent 配置
    let config = AgentConfig {
        llm: LlmConfig {
            provider: "openai".to_string(),
            model,
            api_key,
            base_url: None,
            temperature: Some(0.7),
            max_tokens: Some(2048),
        },
        max_iterations: 10,
        max_tool_calls_per_iteration: 5,
        system_prompt: Some(
            "You are a helpful AI assistant with access to tools. \
             Use the available tools to help answer questions and solve problems. \
             Always explain what you're doing when using tools.".to_string()
        ),
    };

    // 创建工具注册表并注册内置工具
    let mut tool_registry = ToolRegistry::new();
    tool_registry.register_tool(Arc::new(EchoTool))?;
    tool_registry.register_tool(Arc::new(CalculatorTool))?;

    info!("Registered {} tools", tool_registry.tool_count());

    // 创建 Agent
    let mut agent = Agent::new(config, tool_registry);

    // 根据命令行参数决定运行模式
    if matches.get_flag("interactive") {
        run_interactive_mode(&mut agent).await?;
    } else {
        run_single_query_mode(&mut agent).await?;
    }

    Ok(())
}

/// 交互模式
async fn run_interactive_mode(agent: &mut Agent) -> Result<(), Box<dyn std::error::Error>> {
    println!("🤖 Minimal Agent - Interactive Mode");
    println!("Type 'quit' or 'exit' to stop, 'help' for available commands");
    println!("Available tools: {:?}", agent.get_tool_registry().get_tool_names());
    println!();

    // 开始新对话
    let conversation_id = agent.start_conversation();
    info!("Started conversation: {}", conversation_id);

    loop {
        print!("👤 You: ");
        io::stdout().flush()?;

        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let input = input.trim();

        if input.is_empty() {
            continue;
        }

        match input.to_lowercase().as_str() {
            "quit" | "exit" => {
                println!("👋 Goodbye!");
                break;
            }
            "help" => {
                print_help();
                continue;
            }
            "status" => {
                print_status(agent);
                continue;
            }
            "history" => {
                print_history(agent);
                continue;
            }
            _ => {}
        }

        print!("🤖 Agent: ");
        io::stdout().flush()?;

        match agent.process_user_input(input.to_string()).await {
            Ok(response) => {
                println!("{}", response);
            }
            Err(e) => {
                error!("Error processing input: {}", e);
                println!("❌ Error: {}", e);
            }
        }

        println!();
    }

    Ok(())
}

/// 单次查询模式
async fn run_single_query_mode(agent: &mut Agent) -> Result<(), Box<dyn std::error::Error>> {
    println!("🤖 Minimal Agent - Single Query Mode");
    print!("Enter your query: ");
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();

    if input.is_empty() {
        println!("No input provided.");
        return Ok(());
    }

    agent.start_conversation();

    match agent.process_user_input(input.to_string()).await {
        Ok(response) => {
            println!("\n🤖 Response: {}", response);
        }
        Err(e) => {
            error!("Error processing input: {}", e);
            println!("❌ Error: {}", e);
        }
    }

    Ok(())
}

/// 打印帮助信息
fn print_help() {
    println!("📚 Available commands:");
    println!("  help     - Show this help message");
    println!("  status   - Show agent status");
    println!("  history  - Show conversation history");
    println!("  quit/exit - Exit the program");
    println!();
    println!("💡 You can ask me to use tools like:");
    println!("  - 'echo hello world' (test the echo tool)");
    println!("  - 'calculate 15 + 25' (use the calculator)");
    println!("  - 'what is 10 * 5?' (math operations)");
}

/// 打印 Agent 状态
fn print_status(agent: &Agent) {
    println!("📊 Agent Status:");
    if let Some(state) = agent.get_current_state() {
        println!("  Current state: {:?}", state);
    } else {
        println!("  No active conversation");
    }
    println!("  Available tools: {:?}", agent.get_tool_registry().get_tool_names());
}

/// 打印对话历史
fn print_history(agent: &Agent) {
    println!("📜 Conversation History:");
    if let Some(messages) = agent.get_conversation_history() {
        for (i, message) in messages.iter().enumerate() {
            let role_icon = match message.role {
                minimal_agent::MessageRole::User => "👤",
                minimal_agent::MessageRole::Assistant => "🤖",
                minimal_agent::MessageRole::System => "⚙️",
                minimal_agent::MessageRole::Tool => "🔧",
            };
            println!("  {}. {} {}: {}", i + 1, role_icon, 
                     format!("{:?}", message.role), message.content);
        }
    } else {
        println!("  No conversation history");
    }
}
