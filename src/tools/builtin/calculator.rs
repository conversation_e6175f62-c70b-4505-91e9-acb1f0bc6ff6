use crate::tools::ToolTrait;
use crate::types::ToolExecutionResult;
use crate::{AgentError, Result};
use async_trait::async_trait;
use serde_json::{json, Value};

/// 简单的计算器工具
pub struct CalculatorTool;

#[async_trait]
impl ToolTrait for CalculatorTool {
    fn name(&self) -> &str {
        "calculator"
    }

    fn description(&self) -> &str {
        "Perform basic arithmetic operations (add, subtract, multiply, divide)"
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "operation": {
                    "type": "string",
                    "enum": ["add", "subtract", "multiply", "divide"],
                    "description": "The arithmetic operation to perform"
                },
                "a": {
                    "type": "number",
                    "description": "The first number"
                },
                "b": {
                    "type": "number",
                    "description": "The second number"
                }
            },
            "required": ["operation", "a", "b"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let operation = parameters
            .get("operation")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing operation"))?;

        let a = parameters
            .get("a")
            .and_then(|v| v.as_f64())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing or invalid parameter 'a'"))?;

        let b = parameters
            .get("b")
            .and_then(|v| v.as_f64())
            .ok_or_else(|| AgentError::invalid_tool_parameters("Missing or invalid parameter 'b'"))?;

        let result = match operation {
            "add" => a + b,
            "subtract" => a - b,
            "multiply" => a * b,
            "divide" => {
                if b == 0.0 {
                    return Ok(ToolExecutionResult::Error {
                        error: "Division by zero".to_string(),
                    });
                }
                a / b
            }
            _ => {
                return Ok(ToolExecutionResult::Error {
                    error: format!("Unknown operation: {}", operation),
                });
            }
        };

        Ok(ToolExecutionResult::Success {
            output: json!({
                "operation": operation,
                "a": a,
                "b": b,
                "result": result
            }),
        })
    }
}
