use crate::tools::ToolTrait;
use crate::types::ToolExecutionResult;
use crate::Result;
use async_trait::async_trait;
use serde_json::{json, Value};

/// 简单的回声工具，用于测试
pub struct EchoTool;

#[async_trait]
impl ToolTrait for EchoTool {
    fn name(&self) -> &str {
        "echo"
    }

    fn description(&self) -> &str {
        "Echo back the input message. Useful for testing tool execution."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "message": {
                    "type": "string",
                    "description": "The message to echo back"
                }
            },
            "required": ["message"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let message = parameters
            .get("message")
            .and_then(|v| v.as_str())
            .unwrap_or("No message provided");

        Ok(ToolExecutionResult::Success {
            output: json!({
                "echoed_message": message,
                "timestamp": chrono::Utc::now().to_rfc3339()
            }),
        })
    }
}
