use crate::tools::ToolRegistry;
use crate::types::{Tool<PERSON>all, ToolCallResult, ToolExecutionResult};
use crate::{AgentError, Result};
use std::sync::Arc;
use tracing::{debug, error, info, warn};

/// 工具执行器，负责执行工具调用
pub struct ToolExecutor {
    registry: Arc<ToolRegistry>,
}

impl ToolExecutor {
    /// 创建新的工具执行器
    pub fn new(registry: Arc<ToolRegistry>) -> Self {
        Self { registry }
    }

    /// 执行单个工具调用
    pub async fn execute_tool_call(&self, tool_call: &ToolCall) -> ToolCallResult {
        info!("Executing tool call: {} with id: {}", tool_call.name, tool_call.id);
        debug!("Tool parameters: {}", tool_call.parameters);

        let result = match self.registry.get_tool(&tool_call.name) {
            Some(tool) => {
                match tool.implementation.execute(tool_call.parameters.clone()).await {
                    Ok(result) => {
                        info!("Tool call {} completed successfully", tool_call.id);
                        result
                    }
                    Err(e) => {
                        error!("Tool call {} failed: {}", tool_call.id, e);
                        ToolExecutionResult::Error {
                            error: e.to_string(),
                        }
                    }
                }
            }
            None => {
                warn!("Tool not found: {}", tool_call.name);
                ToolExecutionResult::Error {
                    error: format!("Tool '{}' not found", tool_call.name),
                }
            }
        };

        ToolCallResult {
            tool_call_id: tool_call.id.clone(),
            result,
        }
    }

    /// 执行多个工具调用
    pub async fn execute_tool_calls(&self, tool_calls: &[ToolCall]) -> Vec<ToolCallResult> {
        info!("Executing {} tool calls", tool_calls.len());
        
        let mut results = Vec::new();
        
        // 串行执行工具调用（可以根据需要改为并行）
        for tool_call in tool_calls {
            let result = self.execute_tool_call(tool_call).await;
            results.push(result);
        }
        
        info!("Completed {} tool calls", results.len());
        results
    }

    /// 并行执行多个工具调用
    pub async fn execute_tool_calls_parallel(&self, tool_calls: &[ToolCall]) -> Vec<ToolCallResult> {
        info!("Executing {} tool calls in parallel", tool_calls.len());
        
        let futures: Vec<_> = tool_calls
            .iter()
            .map(|tool_call| self.execute_tool_call(tool_call))
            .collect();
        
        let results = futures::future::join_all(futures).await;
        
        info!("Completed {} tool calls in parallel", results.len());
        results
    }

    /// 验证工具调用参数
    pub fn validate_tool_call(&self, tool_call: &ToolCall) -> Result<()> {
        if !self.registry.has_tool(&tool_call.name) {
            return Err(AgentError::tool_not_found(&tool_call.name));
        }

        // 这里可以添加更多的参数验证逻辑
        // 例如，根据工具的 JSON Schema 验证参数

        Ok(())
    }

    /// 批量验证工具调用
    pub fn validate_tool_calls(&self, tool_calls: &[ToolCall]) -> Result<()> {
        for tool_call in tool_calls {
            self.validate_tool_call(tool_call)?;
        }
        Ok(())
    }
}
