# OpenRouter 集成指南

本指南将帮助您配置和使用 OpenRouter 上的 Claude-4 Opus 模型。

## 什么是 OpenRouter？

OpenRouter 是一个统一的 API 平台，提供对多种大语言模型的访问，包括：
- Anthropic Claude 系列 (Claude-3 Opus, Sonnet, Haiku)
- OpenAI GPT 系列
- Google Gemini
- Meta Llama
- 以及更多开源和商业模型

## 获取 OpenRouter API 密钥

1. 访问 [OpenRouter 官网](https://openrouter.ai/)
2. 注册账户
3. 前往 [API Keys 页面](https://openrouter.ai/keys)
4. 创建新的 API 密钥
5. 复制您的 API 密钥

## 配置 Minimal Agent

### 方法 1: 环境变量 (推荐)

```bash
export OPENROUTER_API_KEY="sk-or-v1-your-api-key-here"
```

### 方法 2: 命令行参数

```bash
cargo run -- --api-key "sk-or-v1-your-api-key-here" --interactive
```

### 方法 3: 配置文件

创建 `config.toml` 文件：

```toml
[llm]
provider = "openrouter"
model = "anthropic/claude-3-opus-20240229"
api_key = "sk-or-v1-your-api-key-here"
temperature = 0.7
max_tokens = 4096
```

## 可用的 Claude 模型

### Claude-3 Opus (最强大)
```bash
cargo run -- --model "anthropic/claude-3-opus-20240229" --interactive
```

### Claude-3 Sonnet (平衡性能和成本)
```bash
cargo run -- --model "anthropic/claude-3-sonnet-20240229" --interactive
```

### Claude-3 Haiku (最快速)
```bash
cargo run -- --model "anthropic/claude-3-haiku-20240307" --interactive
```

## 使用示例

### 基本对话

```bash
# 设置 API 密钥
export OPENROUTER_API_KEY="your-api-key"

# 启动交互模式
cargo run -- --interactive
```

### 单次查询

```bash
export OPENROUTER_API_KEY="your-api-key"
echo "Calculate 15 * 25 and explain the result" | cargo run
```

### 指定不同模型

```bash
# 使用 Claude-3 Sonnet
cargo run -- --model "anthropic/claude-3-sonnet-20240229" --interactive

# 使用 GPT-4 (通过 OpenRouter)
cargo run -- --model "openai/gpt-4-turbo-preview" --interactive
```

## 成本优化建议

1. **选择合适的模型**:
   - Haiku: 最便宜，适合简单任务
   - Sonnet: 中等价格，适合大多数任务
   - Opus: 最贵，适合复杂推理任务

2. **控制 token 使用**:
   ```bash
   cargo run -- --model "anthropic/claude-3-haiku-20240307" --interactive
   ```

3. **监控使用情况**:
   - 在 OpenRouter 控制台查看使用统计
   - 设置使用限制和预算警报

## 故障排除

### 常见错误

1. **401 Unauthorized**
   ```
   Error: OpenRouter API error: {"error":{"message":"No auth credentials found","code":401}}
   ```
   解决方案: 检查 API 密钥是否正确设置

2. **402 Payment Required**
   ```
   Error: OpenRouter API error: {"error":{"message":"Insufficient credits","code":402}}
   ```
   解决方案: 在 OpenRouter 账户中充值

3. **429 Rate Limit**
   ```
   Error: OpenRouter API error: {"error":{"message":"Rate limit exceeded","code":429}}
   ```
   解决方案: 等待一段时间后重试，或升级到更高的限制计划

### 调试技巧

1. **启用详细日志**:
   ```bash
   RUST_LOG=debug cargo run -- --interactive
   ```

2. **测试 API 连接**:
   ```bash
   curl -X POST "https://openrouter.ai/api/v1/chat/completions" \
     -H "Authorization: Bearer $OPENROUTER_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "anthropic/claude-3-haiku-20240307",
       "messages": [{"role": "user", "content": "Hello!"}]
     }'
   ```

## 高级配置

### 自定义系统提示

```bash
cargo run -- --interactive
# 然后在交互模式中，系统会使用预设的系统提示
```

### 调整温度和 token 限制

修改 `src/main.rs` 中的配置，或创建配置文件：

```toml
[llm]
provider = "openrouter"
model = "anthropic/claude-3-opus-20240229"
temperature = 0.3  # 更确定性的输出
max_tokens = 8192  # 更长的响应

[agent]
max_iterations = 15
max_tool_calls_per_iteration = 8
```

## 性能对比

| 模型 | 速度 | 成本 | 推理能力 | 适用场景 |
|------|------|------|----------|----------|
| Claude-3 Haiku | 最快 | 最低 | 良好 | 简单任务、快速响应 |
| Claude-3 Sonnet | 中等 | 中等 | 很好 | 大多数应用场景 |
| Claude-3 Opus | 较慢 | 最高 | 最佳 | 复杂推理、创意任务 |

## 支持和社区

- [OpenRouter 文档](https://openrouter.ai/docs)
- [OpenRouter Discord](https://discord.gg/openrouter)
- [Anthropic Claude 文档](https://docs.anthropic.com/)

## 注意事项

1. **API 密钥安全**: 不要在代码中硬编码 API 密钥
2. **成本控制**: 监控使用情况，设置预算限制
3. **模型选择**: 根据任务复杂度选择合适的模型
4. **速率限制**: 遵守 API 速率限制，避免过于频繁的请求
