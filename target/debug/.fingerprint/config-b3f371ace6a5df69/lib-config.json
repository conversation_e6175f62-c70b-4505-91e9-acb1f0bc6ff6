{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 10639462379859935832, "deps": [[1213098572879462490, "json5_rs", false, 17355835068541398216], [1965680986145237447, "yaml_rust2", false, 15758772100007380645], [2244620803250265856, "ron", false, 16627954679915444640], [6502365400774175331, "nom", false, 3055100526333866024], [6517602928339163454, "path<PERSON><PERSON>", false, 14381377889498126830], [9689903380558560274, "serde", false, 11669935311010533149], [11946729385090170470, "async_trait", false, 2786551838605296543], [13475460906694513802, "convert_case", false, 1783313618194761437], [14618892375165583068, "ini", false, 15828502081611027595], [15367738274754116744, "serde_json", false, 10440454948263015996], [15609422047640926750, "toml", false, 8607407627599455966]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-b3f371ace6a5df69/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}