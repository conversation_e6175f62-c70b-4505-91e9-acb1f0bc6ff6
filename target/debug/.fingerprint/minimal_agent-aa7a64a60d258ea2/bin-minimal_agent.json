{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 14320663804039289461, "profile": 6675295047989516842, "path": 4942398508502643691, "deps": [[2706460456408817945, "futures", false, 612327591541618403], [7244058819997729774, "reqwest", false, 13187768370041286157], [8008191657135824715, "thiserror", false, 15129313711590934046], [8319709847752024821, "uuid", false, 12490503004893698924], [8606274917505247608, "tracing", false, 17284660196195686832], [9538054652646069845, "tokio", false, 13146447666866293041], [9689903380558560274, "serde", false, 11669935311010533149], [9897246384292347999, "chrono", false, 16526908234435504498], [11946729385090170470, "async_trait", false, 2786551838605296543], [12382237672615274180, "config", false, 4356098879952328528], [13625485746686963219, "anyhow", false, 9555734602719130145], [15367738274754116744, "serde_json", false, 10440454948263015996], [15511065171759118073, "minimal_agent", false, 4953500335481056247], [16230660778393187092, "tracing_subscriber", false, 3166147670659452106], [17791399664576300066, "clap", false, 17366321128757762346]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/minimal_agent-aa7a64a60d258ea2/dep-bin-minimal_agent", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}