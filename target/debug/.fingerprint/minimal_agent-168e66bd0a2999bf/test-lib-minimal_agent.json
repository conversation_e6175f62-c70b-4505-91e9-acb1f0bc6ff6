{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 7194334524445633784, "profile": 619605765252926426, "path": 10763286916239946207, "deps": [[7244058819997729774, "reqwest", false, 15754889927172313043], [8008191657135824715, "thiserror", false, 699432480210165696], [8319709847752024821, "uuid", false, 3004660388830627730], [8606274917505247608, "tracing", false, 13488828948415658882], [9538054652646069845, "tokio", false, 18301852907224916917], [9689903380558560274, "serde", false, 15664983490166029783], [9897246384292347999, "chrono", false, 16933001176391627494], [11946729385090170470, "async_trait", false, 2786551838605296543], [12382237672615274180, "config", false, 996754234334683037], [13625485746686963219, "anyhow", false, 1629836972144992897], [15367738274754116744, "serde_json", false, 13883177235949408172], [15766163325958592597, "tokio_test", false, 16679187775846211052], [16230660778393187092, "tracing_subscriber", false, 17118500489821670748], [17791399664576300066, "clap", false, 16376547300997624152]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/minimal_agent-168e66bd0a2999bf/dep-test-lib-minimal_agent", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}