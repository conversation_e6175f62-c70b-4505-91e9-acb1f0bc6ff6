{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 11564921311261875935, "deps": [[5103565458935487, "futures_io", false, 15656628683093632725], [1615478164327904835, "pin_utils", false, 2011822313787281215], [1811549171721445101, "futures_channel", false, 608922185914070194], [1906322745568073236, "pin_project_lite", false, 112714291231645796], [3129130049864710036, "memchr", false, 16565091858469226673], [6955678925937229351, "slab", false, 11512623588029983934], [7013762810557009322, "futures_sink", false, 13776510242286445153], [7620660491849607393, "futures_core", false, 989016875435182914], [10565019901765856648, "futures_macro", false, 2800807931537423238], [16240732885093539806, "futures_task", false, 13483658490684002425]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-a935ebafd07d63b3/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}