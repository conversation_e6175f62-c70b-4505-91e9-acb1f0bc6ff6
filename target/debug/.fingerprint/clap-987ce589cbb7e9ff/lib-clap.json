{"rustc": 15497389221046826682, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9589772125425470163, "path": 15674423206804831573, "deps": [[4925398738524877221, "clap_derive", false, 8132858007361956852], [14814905555676593471, "clap_builder", false, 15129347788750457210]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-987ce589cbb7e9ff/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}