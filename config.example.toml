# Minimal Agent 配置文件示例
# 复制此文件为 config.toml 并修改相应的配置

[llm]
# LLM 提供商 (目前支持 "openai")
provider = "openai"

# 模型名称
model = "gpt-3.5-turbo"

# API 密钥 (也可以通过环境变量 OPENAI_API_KEY 设置)
api_key = "your-openai-api-key-here"

# 可选：自定义 API 基础 URL
# base_url = "https://api.openai.com/v1"

# 可选：温度参数 (0.0 - 2.0)
temperature = 0.7

# 可选：最大 token 数
max_tokens = 2048

[agent]
# 最大推理循环次数
max_iterations = 10

# 每次迭代最大工具调用数
max_tool_calls_per_iteration = 5

# 系统提示词
system_prompt = """You are a helpful AI assistant with access to tools.
Use the available tools to help answer questions and solve problems.
Always explain what you're doing when using tools.
Be concise but thorough in your responses."""
