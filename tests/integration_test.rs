use minimal_agent::{
    tools::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ToolTrait, builtin::{EchoTool, CalculatorTool}},
    types::{<PERSON><PERSON><PERSON>all, ToolExecutionResult},
};
use std::sync::Arc;
use serde_json::json;

#[tokio::test]
async fn test_tool_registry() {
    let mut registry = ToolRegistry::new();
    
    // 注册工具
    registry.register_tool(Arc::new(EchoTool)).unwrap();
    registry.register_tool(Arc::new(CalculatorTool)).unwrap();
    
    // 验证工具数量
    assert_eq!(registry.tool_count(), 2);
    
    // 验证工具名称
    let tool_names = registry.get_tool_names();
    assert!(tool_names.contains(&"echo".to_string()));
    assert!(tool_names.contains(&"calculator".to_string()));
    
    // 验证工具存在
    assert!(registry.has_tool("echo"));
    assert!(registry.has_tool("calculator"));
    assert!(!registry.has_tool("nonexistent"));
}

#[tokio::test]
async fn test_echo_tool() {
    let tool = EchoTool;
    
    // 测试工具基本信息
    assert_eq!(tool.name(), "echo");
    assert!(!tool.description().is_empty());
    
    // 测试工具执行
    let parameters = json!({
        "message": "Hello, World!"
    });
    
    let result = tool.execute(parameters).await.unwrap();
    
    match result {
        ToolExecutionResult::Success { output } => {
            assert_eq!(output["echoed_message"], "Hello, World!");
            assert!(output["timestamp"].is_string());
        }
        ToolExecutionResult::Error { .. } => {
            panic!("Echo tool should not fail");
        }
    }
}

#[tokio::test]
async fn test_calculator_tool() {
    let tool = CalculatorTool;
    
    // 测试工具基本信息
    assert_eq!(tool.name(), "calculator");
    assert!(!tool.description().is_empty());
    
    // 测试加法
    let parameters = json!({
        "operation": "add",
        "a": 10.0,
        "b": 5.0
    });
    
    let result = tool.execute(parameters).await.unwrap();
    
    match result {
        ToolExecutionResult::Success { output } => {
            assert_eq!(output["result"], 15.0);
            assert_eq!(output["operation"], "add");
        }
        ToolExecutionResult::Error { .. } => {
            panic!("Calculator tool should not fail for valid input");
        }
    }
    
    // 测试除零错误
    let parameters = json!({
        "operation": "divide",
        "a": 10.0,
        "b": 0.0
    });
    
    let result = tool.execute(parameters).await.unwrap();
    
    match result {
        ToolExecutionResult::Success { .. } => {
            panic!("Division by zero should return an error");
        }
        ToolExecutionResult::Error { error } => {
            assert_eq!(error, "Division by zero");
        }
    }
}

#[tokio::test]
async fn test_tool_executor() {
    use minimal_agent::tools::ToolExecutor;
    
    let mut registry = ToolRegistry::new();
    registry.register_tool(Arc::new(EchoTool)).unwrap();
    registry.register_tool(Arc::new(CalculatorTool)).unwrap();
    
    let executor = ToolExecutor::new(Arc::new(registry));
    
    // 测试单个工具调用
    let tool_call = ToolCall {
        id: "test-1".to_string(),
        name: "echo".to_string(),
        parameters: json!({
            "message": "Test message"
        }),
    };
    
    let result = executor.execute_tool_call(&tool_call).await;
    assert_eq!(result.tool_call_id, "test-1");
    
    match result.result {
        ToolExecutionResult::Success { output } => {
            assert_eq!(output["echoed_message"], "Test message");
        }
        ToolExecutionResult::Error { .. } => {
            panic!("Tool execution should succeed");
        }
    }
    
    // 测试不存在的工具
    let invalid_tool_call = ToolCall {
        id: "test-2".to_string(),
        name: "nonexistent".to_string(),
        parameters: json!({}),
    };
    
    let result = executor.execute_tool_call(&invalid_tool_call).await;
    match result.result {
        ToolExecutionResult::Success { .. } => {
            panic!("Nonexistent tool should return error");
        }
        ToolExecutionResult::Error { error } => {
            assert!(error.contains("not found"));
        }
    }
}

#[tokio::test]
async fn test_agent_state_manager() {
    use minimal_agent::agent::state::AgentStateManager;
    use minimal_agent::types::{Message, AgentState};
    
    let mut state_manager = AgentStateManager::new();
    
    // 测试创建对话
    let _conversation_id = state_manager.create_conversation();
    assert!(state_manager.get_current_conversation().is_some());
    
    // 测试添加消息
    let message = Message::new_user("Hello".to_string());
    state_manager.add_message_to_current(message).unwrap();
    
    let conversation = state_manager.get_current_conversation().unwrap();
    assert_eq!(conversation.messages.len(), 1);
    
    // 测试设置状态
    state_manager.set_current_state(AgentState::Processing).unwrap();
    let state = state_manager.get_current_state().unwrap();
    matches!(state, AgentState::Processing);
}
